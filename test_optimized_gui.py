#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试优化后的GUI界面
"""

import sys
import os
from pathlib import Path

# 添加项目根目录到Python路径
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

def test_gui_optimization():
    """测试GUI优化功能"""
    try:
        print("=" * 50)
        print("测试优化后的GUI界面")
        print("=" * 50)
        
        # 导入GUI模块
        from deepseek_chat_client.gui import ChatGUI
        
        print("✓ GUI模块导入成功")
        
        # 创建GUI实例
        gui = ChatGUI()
        print("✓ GUI实例创建成功")
        
        # 检查新的折叠功能
        print("\n检查折叠功能:")
        
        # 检查模型部分
        if hasattr(gui, 'model_collapsed'):
            print("✓ 模型部分折叠功能已添加")
        else:
            print("✗ 模型部分折叠功能缺失")
            
        # 检查提示词部分
        if hasattr(gui, 'prompt_collapsed'):
            print("✓ 提示词部分折叠功能已添加")
        else:
            print("✗ 提示词部分折叠功能缺失")
            
        # 检查按钮部分
        if hasattr(gui, 'buttons_collapsed'):
            print("✓ 按钮部分折叠功能已添加")
        else:
            print("✗ 按钮部分折叠功能缺失")
        
        # 检查窗口尺寸优化
        print(f"\n窗口尺寸优化:")
        print(f"✓ 窗口宽度: {gui.window_width} (优化后应为900)")
        print(f"✓ 字体大小: {gui.font_size} (优化后应为9)")
        
        # 测试折叠功能
        print("\n测试折叠功能:")
        try:
            gui._toggle_model_section()
            print("✓ 模型部分折叠测试通过")
        except Exception as e:
            print(f"✗ 模型部分折叠测试失败: {e}")
            
        try:
            gui._toggle_prompt_section()
            print("✓ 提示词部分折叠测试通过")
        except Exception as e:
            print(f"✗ 提示词部分折叠测试失败: {e}")
            
        try:
            gui._toggle_buttons_section()
            print("✓ 按钮部分折叠测试通过")
        except Exception as e:
            print(f"✗ 按钮部分折叠测试失败: {e}")
        
        print("\n" + "=" * 50)
        print("GUI优化测试完成！")
        print("=" * 50)
        print("\n界面特性:")
        print("1. ✓ 模型设置部分 - 可折叠，包含模型选择、Token和温度设置")
        print("2. ✓ 提示词设置部分 - 可折叠，包含提示词文件选择")
        print("3. ✓ 操作按钮部分 - 可折叠，包含所有操作按钮")
        print("4. ✓ 界面宽度优化 - 从1440px减少到900px")
        print("5. ✓ 字体大小优化 - 从10减少到9")
        print("6. ✓ 最小窗口宽度 - 从600px减少到500px")
        
        print("\n使用说明:")
        print("- 点击每个部分标题旁的 ▼/▶ 按钮来折叠/展开该部分")
        print("- 折叠后会显示该部分的关键信息")
        print("- 界面更加紧凑，适合小屏幕使用")
        
        # 启动GUI（注释掉以避免阻塞测试）
        # gui.run()
        
        return True
        
    except Exception as e:
        print(f"✗ GUI优化测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    success = test_gui_optimization()
    if success:
        print("\n🎉 所有测试通过！GUI优化成功！")
        sys.exit(0)
    else:
        print("\n❌ 测试失败，请检查代码。")
        sys.exit(1)
