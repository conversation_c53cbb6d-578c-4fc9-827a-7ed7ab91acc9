#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
自动测试面板响应式布局测试脚本
"""

import sys
import tkinter as tk
from pathlib import Path
import time

# 添加项目根目录到Python路径
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

def test_layout_at_different_sizes():
    """测试不同窗口尺寸下的布局效果"""
    try:
        from deepseek_chat_client.gui import ChatGUI
        
        print("=" * 60)
        print("🧪 自动测试面板响应式布局测试")
        print("=" * 60)
        
        # 创建GUI实例
        gui = ChatGUI()
        
        # 测试不同的窗口尺寸
        test_sizes = [
            (1200, 800, "大屏幕 - 完整模式"),
            (1000, 600, "中等屏幕 - 完整模式"),
            (800, 600, "小屏幕 - 紧凑模式"),
            (600, 400, "极小屏幕 - 折叠模式"),
            (400, 300, "最小屏幕 - 折叠模式")
        ]
        
        print("开始测试不同窗口尺寸...")
        print("注意：请观察自动测试面板的布局变化")
        print()
        
        for width, height, description in test_sizes:
            print(f"测试尺寸: {width}x{height} ({description})")
            
            # 设置窗口大小
            gui.root.geometry(f"{width}x{height}")
            gui.root.update()
            
            # 等待布局更新
            time.sleep(0.5)
            
            # 检查当前布局模式
            if hasattr(gui, 'auto_test_layout_mode'):
                current_mode = gui.auto_test_layout_mode
                print(f"  当前布局模式: {current_mode}")
            
            # 检查面板是否折叠
            if hasattr(gui, 'auto_test_is_collapsed') and gui.auto_test_is_collapsed:
                is_collapsed = gui.auto_test_is_collapsed.get()
                print(f"  面板折叠状态: {'折叠' if is_collapsed else '展开'}")
            
            print("  请检查自动测试面板的显示效果...")
            print()
            
            # 暂停以便观察
            input("按回车键继续下一个测试...")
            print()
        
        print("✓ 布局测试完成")
        print()
        print("手动测试说明：")
        print("1. 拖拽窗口边缘改变大小")
        print("2. 观察自动测试面板的自动调整")
        print("3. 点击折叠按钮测试手动折叠功能")
        print("4. 在不同尺寸下测试所有功能")
        print()
        print("按回车键退出测试...")
        input()
        
        return True
        
    except Exception as e:
        print(f"✗ 布局测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_auto_test_functionality():
    """测试自动测试功能的基本操作"""
    try:
        from deepseek_chat_client.gui import ChatGUI
        
        print("=" * 60)
        print("🔧 自动测试功能基本操作测试")
        print("=" * 60)
        
        gui = ChatGUI()
        
        # 测试控件存在性
        required_widgets = [
            ('auto_select_checkbox', '自动选择复选框'),
            ('auto_execute_checkbox', '自动执行复选框'),
            ('delay_spinbox', '延迟设置'),
            ('rounds_spinbox', '回合数设置'),
            ('auto_test_control_btn', '控制按钮'),
            ('auto_test_status_label', '状态标签'),
            ('auto_test_collapse_btn', '折叠按钮'),
            ('auto_test_quick_status', '快速状态显示')
        ]
        
        print("检查控件存在性...")
        for widget_name, description in required_widgets:
            if hasattr(gui, widget_name):
                print(f"  ✓ {description}")
            else:
                print(f"  ✗ {description} - 缺失")
        
        print()
        print("测试控件功能...")
        
        # 测试自动选择开关
        print("  测试自动选择开关...")
        gui.auto_select_enabled.set(True)
        gui._on_auto_select_toggle()
        print("    ✓ 自动选择开关切换成功")
        
        # 测试自动执行开关
        print("  测试自动执行开关...")
        gui.auto_execute_enabled.set(True)
        gui._on_auto_execute_toggle()
        print("    ✓ 自动执行开关切换成功")
        
        # 测试状态更新
        print("  测试状态更新...")
        gui._update_auto_test_status()
        print("    ✓ 状态更新成功")
        
        # 测试折叠功能
        print("  测试面板折叠功能...")
        gui._toggle_auto_test_panel()
        print("    ✓ 面板折叠成功")
        gui._toggle_auto_test_panel()
        print("    ✓ 面板展开成功")
        
        # 测试布局模式切换
        print("  测试布局模式切换...")
        gui._apply_layout_mode("compact")
        print("    ✓ 紧凑模式应用成功")
        gui._apply_layout_mode("full")
        print("    ✓ 完整模式应用成功")
        
        print()
        print("✓ 功能测试完成")
        
        return True
        
    except Exception as e:
        print(f"✗ 功能测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def main():
    """主测试函数"""
    print("开始自动测试面板优化验证...")
    print()
    
    # 测试基本功能
    print("1. 测试基本功能...")
    if not test_auto_test_functionality():
        print("基本功能测试失败，退出")
        return False
    
    print()
    print("2. 测试响应式布局...")
    if not test_layout_at_different_sizes():
        print("布局测试失败，退出")
        return False
    
    print()
    print("🎉 所有测试完成！")
    print()
    print("优化总结：")
    print("✓ 使用Grid布局管理器替代Pack布局")
    print("✓ 实现响应式设计，支持不同窗口尺寸")
    print("✓ 添加面板折叠功能，节省空间")
    print("✓ 支持紧凑模式和完整模式自动切换")
    print("✓ 添加快速状态显示")
    print("✓ 优化控件间距和大小")
    
    return True

if __name__ == "__main__":
    try:
        success = main()
        sys.exit(0 if success else 1)
    except KeyboardInterrupt:
        print("\n用户中断测试")
        sys.exit(1)
    except Exception as e:
        print(f"测试过程中发生错误: {e}")
        import traceback
        traceback.print_exc()
        sys.exit(1)
