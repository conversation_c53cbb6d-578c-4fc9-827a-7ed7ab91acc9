#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
诊断自动选择定时器问题
"""

import sys
import os
from pathlib import Path
import time
import threading

# 添加项目根目录到Python路径
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

def diagnose_timer_issue():
    """诊断定时器问题"""
    try:
        print("=" * 70)
        print("⏰ 诊断自动选择定时器问题")
        print("=" * 70)
        
        from deepseek_chat_client.gui import ChatGUI
        import tkinter as tk
        import tkinter.ttk as ttk
        
        # 创建GUI实例
        gui = ChatGUI()
        print("✓ GUI实例创建成功")
        
        # 启用自动选择功能
        gui.auto_select_enabled.set(True)
        gui.auto_execute_enabled.set(False)
        print("✓ 启用自动选择功能")
        
        # 记录定时器调用
        timer_calls = []
        original_after = gui.root.after
        
        def tracked_after(delay, func):
            """跟踪after调用"""
            timer_id = original_after(delay, func)
            timer_calls.append({
                'delay': delay,
                'func': func.__name__ if hasattr(func, '__name__') else str(func),
                'timer_id': timer_id,
                'time': time.time()
            })
            print(f"  [TIMER] 设置定时器: 延迟={delay}ms, 函数={func.__name__ if hasattr(func, '__name__') else str(func)}, ID={timer_id}")
            return timer_id
        
        # 替换after方法
        gui.root.after = tracked_after
        
        # 记录_perform_auto_select调用
        perform_calls = []
        original_perform = gui._perform_auto_select
        
        def tracked_perform():
            """跟踪_perform_auto_select调用"""
            perform_calls.append(time.time())
            print(f"  [PERFORM] _perform_auto_select 被调用 (第{len(perform_calls)}次)")
            try:
                original_perform()
            except Exception as e:
                print(f"  [ERROR] _perform_auto_select 执行出错: {e}")
        
        # 替换方法
        gui._perform_auto_select = tracked_perform
        
        # 创建测试按钮并触发第一次自动选择
        print("\n🎯 第一次自动选择测试:")
        
        test_frame = ttk.Frame(gui.root)
        test_button1 = ttk.Button(test_frame, text="选项1")
        test_button2 = ttk.Button(test_frame, text="选项2")
        gui.quick_reply_buttons = [test_frame, test_button1, test_button2]
        
        print("  调用 _check_auto_select_trigger...")
        gui._check_auto_select_trigger()
        
        print(f"  定时器调用次数: {len(timer_calls)}")
        if timer_calls:
            print(f"  最新定时器: 延迟={timer_calls[-1]['delay']}ms")
        
        # 运行事件循环一段时间
        print("  运行事件循环等待定时器触发...")
        start_time = time.time()
        while time.time() - start_time < 4:  # 等待4秒
            gui.root.update()
            time.sleep(0.1)
        
        print(f"  _perform_auto_select 调用次数: {len(perform_calls)}")
        
        # 模拟按钮点击和清理
        print("\n🧹 模拟按钮清理:")
        gui._hide_quick_reply_buttons()
        
        # 第二次测试
        print("\n🎯 第二次自动选择测试:")
        
        test_frame2 = ttk.Frame(gui.root)
        test_button3 = ttk.Button(test_frame2, text="选项3")
        test_button4 = ttk.Button(test_frame2, text="选项4")
        gui.quick_reply_buttons = [test_frame2, test_button3, test_button4]
        
        print("  调用 _check_auto_select_trigger...")
        gui._check_auto_select_trigger()
        
        print(f"  定时器调用次数: {len(timer_calls)}")
        if len(timer_calls) > 1:
            print(f"  最新定时器: 延迟={timer_calls[-1]['delay']}ms")
        
        # 运行事件循环一段时间
        print("  运行事件循环等待定时器触发...")
        start_time = time.time()
        while time.time() - start_time < 4:  # 等待4秒
            gui.root.update()
            time.sleep(0.1)
        
        print(f"  _perform_auto_select 调用次数: {len(perform_calls)}")
        
        # 分析结果
        print("\n📊 分析结果:")
        print(f"  总定时器调用次数: {len(timer_calls)}")
        print(f"  总_perform_auto_select调用次数: {len(perform_calls)}")
        
        if len(timer_calls) >= 2:
            print("  ✓ 定时器设置正常")
        else:
            print("  ✗ 定时器设置异常")
        
        if len(perform_calls) >= 2:
            print("  ✓ 自动选择执行正常")
            result = False  # 没有问题
        else:
            print("  ✗ 自动选择执行异常")
            result = True  # 有问题
        
        # 详细信息
        print("\n📋 详细信息:")
        for i, call in enumerate(timer_calls):
            print(f"  定时器{i+1}: 延迟={call['delay']}ms, 函数={call['func']}, ID={call['timer_id']}")
        
        for i, call_time in enumerate(perform_calls):
            print(f"  执行{i+1}: 时间={call_time}")
        
        # 清理
        gui.root.destroy()
        
        return result
        
    except Exception as e:
        print(f"✗ 诊断过程出错: {e}")
        import traceback
        traceback.print_exc()
        return True

if __name__ == "__main__":
    has_issue = diagnose_timer_issue()
    if has_issue:
        print("\n🔧 发现定时器问题")
        sys.exit(1)
    else:
        print("\n✅ 定时器工作正常")
        sys.exit(0)
