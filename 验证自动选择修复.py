#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
验证自动选择功能修复效果
"""

import sys
import os
from pathlib import Path
import time
import threading

# 添加项目根目录到Python路径
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

def verify_auto_select_fix():
    """验证自动选择功能修复效果"""
    try:
        print("=" * 70)
        print("✅ 验证自动选择功能修复效果")
        print("=" * 70)
        
        from deepseek_chat_client.gui import ChatGUI
        import tkinter as tk
        import tkinter.ttk as ttk
        
        # 创建GUI实例
        gui = ChatGUI()
        gui.root.withdraw()  # 隐藏窗口
        print("✓ GUI实例创建成功")
        
        # 启用自动选择功能
        gui.auto_select_enabled.set(True)
        gui.auto_execute_enabled.set(False)
        print("✓ 启用自动选择功能")
        
        # 测试各种快捷回复选项格式
        test_cases = [
            {
                "name": "基本选项格式",
                "content": '''测试响应1
```json
{"quick_replies": ["选项1", "选项2", "选项3"]}
```''',
                "expected_count": 3
            },
            {
                "name": "英文选项格式",
                "content": '''测试响应2
```json
{"quick_replies": ["Option A", "Option B", "Option C"]}
```''',
                "expected_count": 3
            },
            {
                "name": "中英混合格式",
                "content": '''测试响应3
```json
{"quick_replies": ["继续探索", "Return home", "查看状态"]}
```''',
                "expected_count": 3
            },
            {
                "name": "短选项格式",
                "content": '''测试响应4
```json
{"quick_replies": ["是", "否", "等等"]}
```''',
                "expected_count": 3
            },
            {
                "name": "长选项格式",
                "content": '''测试响应5
```json
{"quick_replies": ["询问关于这个地方的历史", "继续向前探索未知区域", "返回安全的地方休息"]}
```''',
                "expected_count": 3
            }
        ]
        
        success_count = 0
        total_count = len(test_cases)
        
        for i, test_case in enumerate(test_cases, 1):
            print(f"\n🧪 测试用例 {i}: {test_case['name']}")
            
            # 解析快捷回复
            gui._parse_and_show_quick_replies(test_case['content'])
            
            # 检查按钮数量
            button_count = len([w for w in gui.quick_reply_buttons if hasattr(w, 'invoke')])
            expected_count = test_case['expected_count']
            
            print(f"  期望按钮数量: {expected_count}")
            print(f"  实际按钮数量: {button_count}")
            
            if button_count == expected_count:
                print(f"  ✅ 测试通过")
                success_count += 1
            else:
                print(f"  ❌ 测试失败")
            
            # 清理按钮
            gui._hide_quick_reply_buttons()
        
        # 测试自动选择触发
        print(f"\n🎯 测试自动选择触发机制:")
        
        # 跟踪自动选择调用
        trigger_calls = []
        original_trigger = gui._check_auto_select_trigger
        
        def tracked_trigger():
            trigger_calls.append(time.time())
            print(f"  [TRIGGER] 第{len(trigger_calls)}次触发检查")
            original_trigger()
        
        gui._check_auto_select_trigger = tracked_trigger
        
        # 显示测试按钮
        test_content = '''自动选择测试
```json
{"quick_replies": ["测试选项1", "测试选项2", "测试选项3"]}
```'''
        
        gui._parse_and_show_quick_replies(test_content)
        
        # 等待自动选择触发
        print("  等待自动选择触发...")
        start_time = time.time()
        while time.time() - start_time < 4:
            gui.root.update()
            time.sleep(0.1)
        
        trigger_success = len(trigger_calls) > 0
        print(f"  触发检查次数: {len(trigger_calls)}")
        
        if trigger_success:
            print(f"  ✅ 自动选择触发正常")
            success_count += 1
        else:
            print(f"  ❌ 自动选择触发失败")
        
        total_count += 1  # 包含自动选择触发测试
        
        # 清理
        gui.root.destroy()
        
        # 总结结果
        print("\n" + "=" * 70)
        print("📊 验证结果总结")
        print("=" * 70)
        
        print(f"✅ 成功测试: {success_count}/{total_count}")
        print(f"❌ 失败测试: {total_count - success_count}/{total_count}")
        print(f"🎯 成功率: {success_count/total_count*100:.1f}%")
        
        if success_count == total_count:
            print("\n🎉 所有测试通过！自动选择功能修复成功！")
            return True
        else:
            print(f"\n⚠️ 部分测试失败，需要进一步调试")
            return False
        
    except Exception as e:
        print(f"✗ 验证过程出错: {e}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    success = verify_auto_select_fix()
    if success:
        print("\n✅ 自动选择功能修复验证成功")
        sys.exit(0)
    else:
        print("\n❌ 自动选择功能修复验证失败")
        sys.exit(1)
