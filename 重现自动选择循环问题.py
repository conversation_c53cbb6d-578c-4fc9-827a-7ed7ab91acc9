#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
重现自动选择循环问题
"""

import sys
import os
from pathlib import Path
import time
import threading

# 添加项目根目录到Python路径
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

def reproduce_auto_select_loop():
    """重现自动选择循环问题"""
    try:
        print("=" * 70)
        print("🔍 重现自动选择循环问题")
        print("=" * 70)
        
        from deepseek_chat_client.gui import ChatGUI
        import tkinter as tk
        import tkinter.ttk as ttk
        
        # 创建GUI实例
        gui = ChatGUI()
        gui.root.withdraw()  # 隐藏窗口
        print("✓ GUI实例创建成功")
        
        # 启用自动选择功能，设置较短的延迟
        gui.auto_select_enabled.set(True)
        gui.auto_execute_enabled.set(False)
        gui.auto_select_delay.set(1)  # 设置1秒延迟
        print("✓ 启用自动选择功能，延迟1秒")
        
        # 跟踪所有相关方法的调用
        call_log = []
        active_timers = []
        
        # 跟踪root.after调用
        original_after = gui.root.after
        def tracked_after(delay, func):
            timer_id = original_after(delay, func)
            active_timers.append(timer_id)
            call_time = time.time()
            func_name = func.__name__ if hasattr(func, '__name__') else 'lambda'
            call_log.append(f"{call_time:.3f}: root.after({delay}, {func_name}) -> timer_id={timer_id}")
            print(f"  [TIMER] 设置定时器: delay={delay}ms, func={func_name}, timer_id={timer_id}")
            return timer_id
        gui.root.after = tracked_after
        
        # 跟踪_perform_auto_select调用
        original_perform = gui._perform_auto_select
        def tracked_perform():
            call_time = time.time()
            call_log.append(f"{call_time:.3f}: _perform_auto_select 被调用")
            print(f"  [PERFORM] _perform_auto_select 被调用 (第{len([c for c in call_log if '_perform_auto_select' in c])}次)")
            original_perform()
        gui._perform_auto_select = tracked_perform
        
        # 跟踪_execute_selected_option调用
        original_execute = gui._execute_selected_option
        def tracked_execute(option_text):
            call_time = time.time()
            call_log.append(f"{call_time:.3f}: _execute_selected_option 被调用 - {option_text}")
            print(f"  [EXECUTE] _execute_selected_option 被调用: {option_text}")
            print(f"    当前按钮数量: {len(gui.quick_reply_buttons)}")
            original_execute(option_text)
        gui._execute_selected_option = tracked_execute
        
        # 场景1：快速连续显示快捷回复
        print("\n🎯 场景1：快速连续显示快捷回复")
        
        test_content1 = '''AI响应1
```json
{"quick_replies": ["选项1", "选项2", "选项3"]}
```'''
        
        gui._parse_and_show_quick_replies(test_content1)
        print("  显示第一组快捷回复")
        
        # 等待0.5秒后立即显示第二组（在自动选择触发之前）
        time.sleep(0.5)
        
        test_content2 = '''AI响应2
```json
{"quick_replies": ["选项A", "选项B", "选项C"]}
```'''
        
        gui._parse_and_show_quick_replies(test_content2)
        print("  显示第二组快捷回复（在第一组自动选择之前）")
        
        # 运行事件循环，观察是否有重复触发
        print("  运行事件循环，观察重复触发...")
        start_time = time.time()
        loop_count = 0
        
        while time.time() - start_time < 8:  # 运行8秒
            gui.root.update()
            time.sleep(0.1)
            loop_count += 1
            
            # 检查是否有过多的执行
            execute_calls = [c for c in call_log if '_execute_selected_option' in c]
            if len(execute_calls) > 2:
                print(f"\n⚠️ 检测到过多的执行调用: {len(execute_calls)}次")
                break
        
        # 场景2：模拟用户快速操作
        print("\n🎯 场景2：模拟用户快速操作")
        
        # 清理之前的状态
        gui._hide_quick_reply_buttons()
        time.sleep(0.1)
        
        test_content3 = '''AI响应3
```json
{"quick_replies": ["快速1", "快速2", "快速3"]}
```'''
        
        gui._parse_and_show_quick_replies(test_content3)
        print("  显示第三组快捷回复")
        
        # 等待0.3秒后手动清理按钮（模拟用户快速操作）
        time.sleep(0.3)
        gui._hide_quick_reply_buttons()
        print("  手动清理按钮（模拟用户操作）")
        
        # 继续运行事件循环，看是否还有定时器触发
        print("  继续运行事件循环，观察定时器...")
        start_time = time.time()
        
        while time.time() - start_time < 5:  # 运行5秒
            gui.root.update()
            time.sleep(0.1)
            
            # 检查是否有执行调用（应该没有，因为按钮已被清理）
            execute_calls = [c for c in call_log if '_execute_selected_option' in c and time.time() - start_time < 5]
            recent_executes = [c for c in execute_calls if 'AI响应3' in str(c) or '快速' in str(c)]
            if recent_executes:
                print(f"\n⚠️ 检测到按钮清理后仍有执行调用")
                break
        
        # 分析结果
        print("\n📊 最终分析:")
        
        perform_calls = [c for c in call_log if '_perform_auto_select' in c]
        execute_calls = [c for c in call_log if '_execute_selected_option' in c]
        timer_calls = [c for c in call_log if 'root.after' in c]
        
        print(f"  _perform_auto_select 调用次数: {len(perform_calls)}")
        print(f"  _execute_selected_option 调用次数: {len(execute_calls)}")
        print(f"  root.after 调用次数: {len(timer_calls)}")
        print(f"  活跃定时器数量: {len(active_timers)}")
        
        # 详细时间分析
        print("\n⏰ 关键调用时间序列:")
        for log_entry in call_log:
            if '_perform_auto_select' in log_entry or '_execute_selected_option' in log_entry:
                print(f"  {log_entry}")
        
        # 问题判断
        if len(execute_calls) > len(perform_calls):
            print(f"\n❌ 检测到异常：执行次数({len(execute_calls)}) > 触发次数({len(perform_calls)})")
            result = True
        elif len(timer_calls) > len(perform_calls) * 2:  # 每次perform应该最多设置2个定时器
            print(f"\n⚠️ 检测到定时器过多：{len(timer_calls)} 个定时器，{len(perform_calls)} 次触发")
            result = True
        else:
            print(f"\n✅ 调用次数正常")
            result = False
        
        # 清理
        gui.root.destroy()
        
        return result
        
    except Exception as e:
        print(f"✗ 测试过程出错: {e}")
        import traceback
        traceback.print_exc()
        return True

if __name__ == "__main__":
    has_issue = reproduce_auto_select_loop()
    if has_issue:
        print("\n🔧 确认存在自动选择循环问题")
        sys.exit(1)
    else:
        print("\n✅ 自动选择功能正常")
        sys.exit(0)
