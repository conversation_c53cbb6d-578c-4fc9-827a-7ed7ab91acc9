#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
验证自动选择循环问题修复效果
"""

import sys
import os
from pathlib import Path
import time
import threading

# 添加项目根目录到Python路径
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

def verify_auto_select_loop_fix():
    """验证自动选择循环问题修复效果"""
    try:
        print("=" * 70)
        print("✅ 验证自动选择循环问题修复效果")
        print("=" * 70)
        
        from deepseek_chat_client.gui import ChatGUI
        import tkinter as tk
        import tkinter.ttk as ttk
        
        # 创建GUI实例
        gui = ChatGUI()
        gui.root.withdraw()  # 隐藏窗口
        print("✓ GUI实例创建成功")
        
        # 启用自动选择功能，设置较短的延迟
        gui.auto_select_enabled.set(True)
        gui.auto_execute_enabled.set(False)
        gui.auto_select_delay.set(1)  # 设置1秒延迟
        print("✓ 启用自动选择功能，延迟1秒")
        
        # 跟踪关键方法调用
        perform_calls = []
        execute_calls = []
        timer_cancel_calls = []
        
        # 跟踪_perform_auto_select调用
        original_perform = gui._perform_auto_select
        def tracked_perform():
            perform_calls.append(time.time())
            print(f"  [PERFORM] _perform_auto_select 被调用 (第{len(perform_calls)}次)")
            original_perform()
        gui._perform_auto_select = tracked_perform
        
        # 跟踪_execute_selected_option调用
        original_execute = gui._execute_selected_option
        def tracked_execute(option_text):
            execute_calls.append((time.time(), option_text))
            print(f"  [EXECUTE] _execute_selected_option 被调用: {option_text} (第{len(execute_calls)}次)")
            original_execute(option_text)
        gui._execute_selected_option = tracked_execute
        
        # 跟踪root.after_cancel调用
        original_cancel = gui.root.after_cancel
        def tracked_cancel(timer_id):
            timer_cancel_calls.append((time.time(), timer_id))
            print(f"  [CANCEL] 取消定时器: {timer_id}")
            original_cancel(timer_id)
        gui.root.after_cancel = tracked_cancel
        
        # 测试场景1：快速连续显示快捷回复
        print("\n🎯 测试场景1：快速连续显示快捷回复")
        
        # 第一组快捷回复
        test_content1 = '''AI响应1
```json
{"quick_replies": ["选项1", "选项2", "选项3"]}
```'''
        
        gui._parse_and_show_quick_replies(test_content1)
        print("  显示第一组快捷回复")
        
        # 等待0.5秒后立即显示第二组（在自动选择触发之前）
        time.sleep(0.5)
        
        # 第二组快捷回复
        test_content2 = '''AI响应2
```json
{"quick_replies": ["选项A", "选项B", "选项C"]}
```'''
        
        gui._parse_and_show_quick_replies(test_content2)
        print("  显示第二组快捷回复（应该取消第一组的定时器）")
        
        # 运行事件循环等待自动选择完成
        print("  等待自动选择完成...")
        start_time = time.time()
        
        while time.time() - start_time < 4:  # 等待4秒
            gui.root.update()
            time.sleep(0.1)
            
            # 如果已经有执行调用，可以提前结束
            if len(execute_calls) >= 1:
                time.sleep(0.5)  # 再等待0.5秒确保没有额外调用
                break
        
        # 测试场景2：用户手动清理按钮
        print("\n🎯 测试场景2：用户手动清理按钮")
        
        # 清理之前的状态
        gui._hide_quick_reply_buttons()
        time.sleep(0.1)
        
        # 记录清理前的调用次数
        perform_before = len(perform_calls)
        execute_before = len(execute_calls)
        
        # 显示新的快捷回复
        test_content3 = '''AI响应3
```json
{"quick_replies": ["新选项1", "新选项2", "新选项3"]}
```'''
        
        gui._parse_and_show_quick_replies(test_content3)
        print("  显示第三组快捷回复")
        
        # 等待0.3秒后手动清理按钮
        time.sleep(0.3)
        gui._hide_quick_reply_buttons()
        print("  手动清理按钮（应该取消定时器）")
        
        # 继续运行事件循环，确保没有额外的执行
        print("  继续运行事件循环，确保没有额外执行...")
        start_time = time.time()
        
        while time.time() - start_time < 3:  # 等待3秒
            gui.root.update()
            time.sleep(0.1)
        
        # 分析结果
        print("\n📊 修复效果分析:")
        
        print(f"  _perform_auto_select 总调用次数: {len(perform_calls)}")
        print(f"  _execute_selected_option 总调用次数: {len(execute_calls)}")
        print(f"  定时器取消次数: {len(timer_cancel_calls)}")
        
        # 详细调用信息
        print("\n⏰ 详细调用信息:")
        for i, call_time in enumerate(perform_calls):
            print(f"  _perform_auto_select 第{i+1}次: {call_time:.3f}")
        
        for i, (call_time, option) in enumerate(execute_calls):
            print(f"  _execute_selected_option 第{i+1}次: {call_time:.3f} - {option}")
        
        for i, (call_time, timer_id) in enumerate(timer_cancel_calls):
            print(f"  定时器取消 第{i+1}次: {call_time:.3f} - {timer_id}")
        
        # 判断修复效果
        success = True
        issues = []
        
        # 检查1：perform调用次数应该合理（最多2次，因为有2组有效的快捷回复）
        if len(perform_calls) > 2:
            success = False
            issues.append(f"_perform_auto_select 调用过多: {len(perform_calls)} 次")
        
        # 检查2：execute调用次数应该等于或少于perform调用次数
        if len(execute_calls) > len(perform_calls):
            success = False
            issues.append(f"_execute_selected_option 调用过多: {len(execute_calls)} 次 > {len(perform_calls)} 次")
        
        # 检查3：应该有定时器取消操作
        if len(timer_cancel_calls) == 0:
            success = False
            issues.append("没有检测到定时器取消操作")
        
        # 检查4：场景2中不应该有额外的执行（因为按钮被手动清理了）
        perform_after = len(perform_calls) - perform_before
        execute_after = len(execute_calls) - execute_before
        
        if execute_after > 0:
            success = False
            issues.append(f"场景2中检测到额外执行: {execute_after} 次")
        
        # 输出结果
        if success:
            print(f"\n✅ 修复验证成功！")
            print("  - 自动选择调用次数合理")
            print("  - 定时器正确取消")
            print("  - 没有重复触发问题")
        else:
            print(f"\n❌ 修复验证失败！")
            for issue in issues:
                print(f"  - {issue}")
        
        # 清理
        gui.root.destroy()
        
        return success
        
    except Exception as e:
        print(f"✗ 验证过程出错: {e}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    success = verify_auto_select_loop_fix()
    if success:
        print("\n🎉 自动选择循环问题修复验证成功")
        sys.exit(0)
    else:
        print("\n🔧 自动选择循环问题仍需进一步修复")
        sys.exit(1)
