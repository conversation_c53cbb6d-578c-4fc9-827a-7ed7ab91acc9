#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试真实应用中的自动选择循环问题
"""

import sys
import os
from pathlib import Path
import time
import threading

# 添加项目根目录到Python路径
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

def test_real_auto_select_loop():
    """测试真实应用中的自动选择循环问题"""
    try:
        print("=" * 70)
        print("🔍 测试真实应用中的自动选择循环问题")
        print("=" * 70)
        
        from deepseek_chat_client.gui import ChatGUI
        import tkinter as tk
        import tkinter.ttk as ttk
        
        # 创建GUI实例
        gui = ChatGUI()
        gui.root.withdraw()  # 隐藏窗口
        print("✓ GUI实例创建成功")
        
        # 启用自动选择功能
        gui.auto_select_enabled.set(True)
        gui.auto_execute_enabled.set(False)
        print("✓ 启用自动选择功能")
        
        # 跟踪所有相关方法的调用
        call_log = []
        timer_ids = []  # 跟踪定时器ID
        
        # 跟踪root.after调用
        original_after = gui.root.after
        def tracked_after(delay, func):
            call_time = time.time()
            timer_id = original_after(delay, func)
            timer_ids.append(timer_id)
            call_log.append(f"{call_time:.3f}: root.after({delay}, {func.__name__ if hasattr(func, '__name__') else 'lambda'}) -> timer_id={timer_id}")
            print(f"  [TIMER] 设置定时器: delay={delay}ms, func={func.__name__ if hasattr(func, '__name__') else 'lambda'}, timer_id={timer_id}")
            return timer_id
        gui.root.after = tracked_after
        
        # 跟踪_perform_auto_select调用
        original_perform = gui._perform_auto_select
        def tracked_perform():
            call_time = time.time()
            call_log.append(f"{call_time:.3f}: _perform_auto_select 被调用")
            print(f"  [PERFORM] _perform_auto_select 被调用 (第{len([c for c in call_log if '_perform_auto_select' in c])}次)")
            
            # 检查当前按钮状态
            print(f"    当前按钮数量: {len(gui.quick_reply_buttons)}")
            print(f"    自动选择启用: {gui.auto_select_enabled.get()}")
            print(f"    自动执行启用: {gui.auto_execute_enabled.get()}")
            
            original_perform()
        gui._perform_auto_select = tracked_perform
        
        # 跟踪_execute_selected_option调用
        original_execute = gui._execute_selected_option
        def tracked_execute(option_text):
            call_time = time.time()
            call_log.append(f"{call_time:.3f}: _execute_selected_option 被调用 - {option_text}")
            print(f"  [EXECUTE] _execute_selected_option 被调用: {option_text}")
            
            # 检查执行前的状态
            print(f"    执行前按钮数量: {len(gui.quick_reply_buttons)}")
            
            original_execute(option_text)
            
            # 检查执行后的状态
            print(f"    执行后按钮数量: {len(gui.quick_reply_buttons)}")
        gui._execute_selected_option = tracked_execute
        
        # 跟踪_parse_and_show_quick_replies调用
        original_parse = gui._parse_and_show_quick_replies
        def tracked_parse(content):
            call_time = time.time()
            call_log.append(f"{call_time:.3f}: _parse_and_show_quick_replies 被调用")
            print(f"  [PARSE] _parse_and_show_quick_replies 被调用")
            original_parse(content)
        gui._parse_and_show_quick_replies = tracked_parse
        
        # 模拟完整的消息流程
        print("\n🎯 模拟完整的消息流程:")
        
        # 第一轮：显示快捷回复
        print("\n--- 第一轮：显示快捷回复 ---")
        test_content1 = '''AI响应1
```json
{"quick_replies": ["选项A", "选项B", "选项C"]}
```'''
        
        gui._parse_and_show_quick_replies(test_content1)
        
        # 运行事件循环等待自动选择
        print("  等待自动选择...")
        start_time = time.time()
        while time.time() - start_time < 6:  # 等待6秒
            gui.root.update()
            time.sleep(0.1)
            
            # 检查是否有过多的调用
            perform_calls = [c for c in call_log if '_perform_auto_select' in c]
            if len(perform_calls) > 2:
                print(f"\n⚠️ 检测到过多的_perform_auto_select调用: {len(perform_calls)}次")
                break
        
        # 第二轮：模拟API响应后显示新的快捷回复
        print("\n--- 第二轮：模拟API响应后显示新的快捷回复 ---")
        test_content2 = '''AI响应2
```json
{"quick_replies": ["选项X", "选项Y", "选项Z"]}
```'''
        
        # 使用root.after模拟API响应的异步调用
        gui.root.after(0, lambda: gui._parse_and_show_quick_replies(test_content2))
        
        # 继续运行事件循环
        print("  等待第二轮自动选择...")
        start_time = time.time()
        while time.time() - start_time < 6:  # 等待6秒
            gui.root.update()
            time.sleep(0.1)
            
            # 检查是否有过多的调用
            perform_calls = [c for c in call_log if '_perform_auto_select' in c]
            if len(perform_calls) > 4:
                print(f"\n⚠️ 检测到过多的_perform_auto_select调用: {len(perform_calls)}次")
                break
        
        # 分析结果
        print("\n📊 调用分析:")
        
        parse_calls = [c for c in call_log if '_parse_and_show_quick_replies' in c]
        perform_calls = [c for c in call_log if '_perform_auto_select' in c]
        execute_calls = [c for c in call_log if '_execute_selected_option' in c]
        timer_calls = [c for c in call_log if 'root.after' in c]
        
        print(f"  _parse_and_show_quick_replies 调用次数: {len(parse_calls)}")
        print(f"  _perform_auto_select 调用次数: {len(perform_calls)}")
        print(f"  _execute_selected_option 调用次数: {len(execute_calls)}")
        print(f"  root.after 调用次数: {len(timer_calls)}")
        print(f"  活跃定时器数量: {len(timer_ids)}")
        
        # 详细时间分析
        print("\n⏰ 详细调用时间序列:")
        for i, log_entry in enumerate(call_log):
            if i < 30:  # 只显示前30条
                print(f"  {log_entry}")
            else:
                print(f"  ... (还有 {len(call_log) - 30} 条记录)")
                break
        
        # 问题判断
        expected_perform_calls = 2  # 应该只有2次（每轮一次）
        if len(perform_calls) > expected_perform_calls:
            print(f"\n❌ 检测到重复触发问题！")
            print(f"  期望调用次数: {expected_perform_calls}")
            print(f"  实际调用次数: {len(perform_calls)}")
            print(f"  多余调用次数: {len(perform_calls) - expected_perform_calls}")
            
            # 分析可能的原因
            print("\n🔍 可能的原因分析:")
            if len(timer_calls) > expected_perform_calls:
                print(f"  - 定时器设置过多: {len(timer_calls)} 次")
            
            print(f"  - 当前按钮状态: {len(gui.quick_reply_buttons)} 个按钮")
            print(f"  - 自动选择启用: {gui.auto_select_enabled.get()}")
            
            result = True  # 有问题
        else:
            print(f"\n✅ 自动选择调用次数正常: {len(perform_calls)}")
            result = False  # 没有问题
        
        # 清理
        gui.root.destroy()
        
        return result
        
    except Exception as e:
        print(f"✗ 测试过程出错: {e}")
        import traceback
        traceback.print_exc()
        return True

if __name__ == "__main__":
    has_issue = test_real_auto_select_loop()
    if has_issue:
        print("\n🔧 确认存在自动选择重复触发问题")
        sys.exit(1)
    else:
        print("\n✅ 自动选择功能正常")
        sys.exit(0)
