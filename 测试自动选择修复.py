#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试自动选择功能修复
"""

import sys
import os
from pathlib import Path

# 添加项目根目录到Python路径
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

def test_auto_select_fix():
    """测试自动选择功能修复"""
    try:
        print("=" * 60)
        print("🔧 测试自动选择功能修复")
        print("=" * 60)
        
        # 导入GUI模块
        from deepseek_chat_client.gui import ChatGUI
        
        print("✓ GUI模块导入成功")
        
        # 创建GUI实例
        gui = ChatGUI()
        gui.root.withdraw()  # 隐藏窗口
        print("✓ GUI实例创建成功")
        
        # 模拟快捷回复按钮列表（包含不同类型的控件）
        import tkinter as tk
        import tkinter.ttk as ttk
        
        # 创建测试控件
        test_frame = ttk.Frame(gui.root)
        test_label = ttk.Label(test_frame, text="快捷回复:")
        test_button1 = ttk.Button(test_frame, text="选项1")
        test_button2 = ttk.Button(test_frame, text="选项2")
        test_button3 = ttk.Button(test_frame, text="选项3")
        
        # 模拟quick_reply_buttons列表
        gui.quick_reply_buttons = [
            test_label,      # Label控件（不支持invoke）
            test_frame,      # Frame控件（不支持cget('text')）
            test_button1,    # Button控件（支持所有操作）
            test_button2,    # Button控件（支持所有操作）
            test_button3     # Button控件（支持所有操作）
        ]
        
        print("✓ 模拟快捷回复按钮列表创建成功")
        
        # 测试_perform_auto_select方法
        print("\n🧪 测试_perform_auto_select方法:")
        try:
            # 这个方法应该能够正确处理混合类型的控件列表
            gui._perform_auto_select()
            print("✓ _perform_auto_select方法执行成功，没有出现'unknown option \"-text\"'错误")
        except Exception as e:
            if "unknown option" in str(e) and "-text" in str(e):
                print(f"✗ 仍然存在'-text'选项错误: {e}")
                return False
            else:
                print(f"✓ 没有'-text'选项错误，其他错误可以忽略: {e}")
        
        # 测试_execute_selected_option方法
        print("\n🧪 测试_execute_selected_option方法:")
        try:
            gui._execute_selected_option("选项1")
            print("✓ _execute_selected_option方法执行成功")
        except Exception as e:
            if "unknown option" in str(e) and "-text" in str(e):
                print(f"✗ 仍然存在'-text'选项错误: {e}")
                return False
            else:
                print(f"✓ 没有'-text'选项错误，其他错误可以忽略: {e}")
        
        # 清理测试控件
        gui.root.destroy()
        
        print("\n" + "=" * 60)
        print("🎉 自动选择功能修复测试完成！")
        print("=" * 60)
        print("\n修复内容:")
        print("1. ✓ 修复了_perform_auto_select中的cget('text')调用")
        print("2. ✓ 添加了异常处理，避免不支持的控件导致错误")
        print("3. ✓ 确保只对Button类型控件执行cget('text')操作")
        print("4. ✓ 修复了'unknown option \"-text\"'错误")
        
        return True
        
    except Exception as e:
        print(f"✗ 测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    success = test_auto_select_fix()
    if success:
        print("\n🎉 修复成功！自动选择功能现在可以正常工作了！")
        sys.exit(0)
    else:
        print("\n❌ 修复失败，请检查代码。")
        sys.exit(1)
