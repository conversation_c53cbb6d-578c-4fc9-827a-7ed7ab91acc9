#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
诊断自动选择功能重复触发问题
"""

import sys
import os
from pathlib import Path
import time
import threading

# 添加项目根目录到Python路径
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

def diagnose_auto_select_loop():
    """诊断自动选择功能重复触发问题"""
    try:
        print("=" * 70)
        print("🔍 诊断自动选择功能重复触发问题")
        print("=" * 70)
        
        from deepseek_chat_client.gui import ChatGUI
        import tkinter as tk
        import tkinter.ttk as ttk
        
        # 创建GUI实例
        gui = ChatGUI()
        gui.root.withdraw()  # 隐藏窗口
        print("✓ GUI实例创建成功")
        
        # 启用自动选择功能
        gui.auto_select_enabled.set(True)
        gui.auto_execute_enabled.set(False)
        print("✓ 启用自动选择功能")
        
        # 跟踪所有相关方法的调用
        call_log = []
        
        # 跟踪_check_auto_select_trigger调用
        original_check_trigger = gui._check_auto_select_trigger
        def tracked_check_trigger():
            call_time = time.time()
            call_log.append(f"{call_time:.3f}: _check_auto_select_trigger 被调用")
            print(f"  [TRIGGER] _check_auto_select_trigger 被调用 (第{len([c for c in call_log if '_check_auto_select_trigger' in c])}次)")
            original_check_trigger()
        gui._check_auto_select_trigger = tracked_check_trigger
        
        # 跟踪_perform_auto_select调用
        original_perform = gui._perform_auto_select
        def tracked_perform():
            call_time = time.time()
            call_log.append(f"{call_time:.3f}: _perform_auto_select 被调用")
            print(f"  [PERFORM] _perform_auto_select 被调用 (第{len([c for c in call_log if '_perform_auto_select' in c])}次)")
            original_perform()
        gui._perform_auto_select = tracked_perform
        
        # 跟踪_execute_selected_option调用
        original_execute = gui._execute_selected_option
        def tracked_execute(option_text):
            call_time = time.time()
            call_log.append(f"{call_time:.3f}: _execute_selected_option 被调用 - {option_text}")
            print(f"  [EXECUTE] _execute_selected_option 被调用: {option_text}")
            original_execute(option_text)
        gui._execute_selected_option = tracked_execute
        
        # 跟踪_hide_quick_reply_buttons调用
        original_hide = gui._hide_quick_reply_buttons
        def tracked_hide():
            call_time = time.time()
            call_log.append(f"{call_time:.3f}: _hide_quick_reply_buttons 被调用")
            print(f"  [HIDE] _hide_quick_reply_buttons 被调用")
            original_hide()
        gui._hide_quick_reply_buttons = tracked_hide
        
        # 跟踪_send_message调用
        original_send = gui._send_message
        def tracked_send(is_quick_reply=False):
            call_time = time.time()
            call_log.append(f"{call_time:.3f}: _send_message 被调用 - is_quick_reply={is_quick_reply}")
            print(f"  [SEND] _send_message 被调用 - is_quick_reply={is_quick_reply}")
            # 不实际发送消息，只模拟清理按钮
            if is_quick_reply:
                gui._hide_quick_reply_buttons()
        gui._send_message = tracked_send
        
        # 模拟显示快捷回复
        print("\n🎯 第一次显示快捷回复:")
        test_content = '''测试响应
```json
{"quick_replies": ["选项1", "选项2", "选项3"]}
```'''
        
        gui._parse_and_show_quick_replies(test_content)
        
        # 运行事件循环，观察重复触发
        print("\n⏰ 运行事件循环，观察是否重复触发...")
        start_time = time.time()
        loop_count = 0
        max_loops = 100  # 最多运行100次循环
        
        while time.time() - start_time < 10 and loop_count < max_loops:  # 最多运行10秒
            gui.root.update()
            time.sleep(0.1)
            loop_count += 1
            
            # 检查是否有重复触发
            trigger_calls = [c for c in call_log if '_check_auto_select_trigger' in c]
            perform_calls = [c for c in call_log if '_perform_auto_select' in c]
            execute_calls = [c for c in call_log if '_execute_selected_option' in c]
            
            if len(perform_calls) > 3:  # 如果执行超过3次，说明有问题
                print(f"\n⚠️ 检测到重复触发！已执行 {len(perform_calls)} 次")
                break
        
        # 分析调用日志
        print("\n📊 调用日志分析:")
        print(f"  总事件循环次数: {loop_count}")
        
        trigger_calls = [c for c in call_log if '_check_auto_select_trigger' in c]
        perform_calls = [c for c in call_log if '_perform_auto_select' in c]
        execute_calls = [c for c in call_log if '_execute_selected_option' in c]
        hide_calls = [c for c in call_log if '_hide_quick_reply_buttons' in c]
        send_calls = [c for c in call_log if '_send_message' in c]
        
        print(f"  _check_auto_select_trigger 调用次数: {len(trigger_calls)}")
        print(f"  _perform_auto_select 调用次数: {len(perform_calls)}")
        print(f"  _execute_selected_option 调用次数: {len(execute_calls)}")
        print(f"  _hide_quick_reply_buttons 调用次数: {len(hide_calls)}")
        print(f"  _send_message 调用次数: {len(send_calls)}")
        
        # 详细时间分析
        print("\n⏰ 详细调用时间序列:")
        for log_entry in call_log[:20]:  # 只显示前20条
            print(f"  {log_entry}")
        
        if len(call_log) > 20:
            print(f"  ... (还有 {len(call_log) - 20} 条记录)")
        
        # 问题分析
        print("\n🔍 问题分析:")
        
        if len(perform_calls) > 1:
            print("  ❌ 检测到重复触发问题！")
            print("  可能原因:")
            print("    1. 定时器没有被正确取消")
            print("    2. 按钮没有被正确清理")
            print("    3. 触发条件没有被正确重置")
            
            # 检查按钮状态
            print(f"  当前快捷回复按钮数量: {len(gui.quick_reply_buttons)}")
            print(f"  自动选择启用状态: {gui.auto_select_enabled.get()}")
            print(f"  自动执行启用状态: {gui.auto_execute_enabled.get()}")
            
            result = True  # 有问题
        else:
            print("  ✅ 未检测到重复触发问题")
            result = False  # 没有问题
        
        # 清理
        gui.root.destroy()
        
        return result
        
    except Exception as e:
        print(f"✗ 诊断过程出错: {e}")
        import traceback
        traceback.print_exc()
        return True

if __name__ == "__main__":
    has_issue = diagnose_auto_select_loop()
    if has_issue:
        print("\n🔧 确认存在自动选择重复触发问题")
        sys.exit(1)
    else:
        print("\n✅ 自动选择功能正常")
        sys.exit(0)
