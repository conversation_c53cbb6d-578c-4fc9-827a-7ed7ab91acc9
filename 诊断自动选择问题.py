#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
诊断自动选择功能只执行一次后停止工作的问题
"""

import sys
import os
from pathlib import Path
import time
import threading

# 添加项目根目录到Python路径
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

def diagnose_auto_select_issue():
    """诊断自动选择功能问题"""
    try:
        print("=" * 70)
        print("🔍 诊断自动选择功能问题")
        print("=" * 70)
        
        from deepseek_chat_client.gui import ChatGUI
        import tkinter as tk
        import tkinter.ttk as ttk
        
        # 创建GUI实例
        gui = ChatGUI()
        gui.root.withdraw()  # 隐藏窗口
        print("✓ GUI实例创建成功")
        
        # 启用自动选择功能
        gui.auto_select_enabled.set(True)
        gui.auto_execute_enabled.set(False)  # 只启用自动选择，不启用自动执行
        print("✓ 启用自动选择功能（禁用自动执行）")
        
        # 模拟第一次快捷回复按钮创建
        print("\n🧪 模拟第一次快捷回复按钮创建:")
        
        # 创建测试按钮
        test_frame = ttk.Frame(gui.root)
        test_label = ttk.Label(test_frame, text="快捷回复:")
        test_button1 = ttk.Button(test_frame, text="选项1", command=lambda: print("点击了选项1"))
        test_button2 = ttk.Button(test_frame, text="选项2", command=lambda: print("点击了选项2"))
        
        # 设置快捷回复按钮列表
        gui.quick_reply_buttons = [test_label, test_frame, test_button1, test_button2]
        print(f"  快捷回复按钮数量: {len(gui.quick_reply_buttons)}")
        
        # 检查触发条件
        print("\n🔍 检查自动选择触发条件:")
        print(f"  auto_select_enabled: {gui.auto_select_enabled.get()}")
        print(f"  auto_execute_enabled: {gui.auto_execute_enabled.get()}")
        print(f"  quick_reply_buttons存在: {bool(gui.quick_reply_buttons)}")
        
        # 测试第一次自动选择触发
        print("\n🎯 测试第一次自动选择触发:")
        gui._check_auto_select_trigger()
        print("  ✓ _check_auto_select_trigger 调用完成")
        
        # 模拟按钮点击后的清理过程
        print("\n🧹 模拟按钮点击后的清理过程:")
        
        # 记录原始的_hide_quick_reply_buttons方法
        original_hide_method = gui._hide_quick_reply_buttons
        
        def enhanced_hide_method():
            """增强的隐藏方法，添加诊断信息"""
            print("  [DEBUG] _hide_quick_reply_buttons 被调用")
            print(f"  [DEBUG] 清理前按钮数量: {len(gui.quick_reply_buttons)}")
            original_hide_method()
            print(f"  [DEBUG] 清理后按钮数量: {len(gui.quick_reply_buttons)}")
            print("  [DEBUG] 快捷回复按钮已清理")
        
        # 替换方法
        gui._hide_quick_reply_buttons = enhanced_hide_method
        
        # 模拟点击按钮
        print("  模拟点击选项1...")
        test_button1.invoke()
        
        # 模拟清理按钮
        print("  执行按钮清理...")
        gui._hide_quick_reply_buttons()
        
        # 模拟第二次快捷回复按钮创建
        print("\n🧪 模拟第二次快捷回复按钮创建:")
        
        # 创建新的测试按钮
        test_frame2 = ttk.Frame(gui.root)
        test_label2 = ttk.Label(test_frame2, text="快捷回复:")
        test_button3 = ttk.Button(test_frame2, text="选项3", command=lambda: print("点击了选项3"))
        test_button4 = ttk.Button(test_frame2, text="选项4", command=lambda: print("点击了选项4"))
        
        # 设置新的快捷回复按钮列表
        gui.quick_reply_buttons = [test_label2, test_frame2, test_button3, test_button4]
        print(f"  新的快捷回复按钮数量: {len(gui.quick_reply_buttons)}")
        
        # 检查第二次触发条件
        print("\n🔍 检查第二次自动选择触发条件:")
        print(f"  auto_select_enabled: {gui.auto_select_enabled.get()}")
        print(f"  auto_execute_enabled: {gui.auto_execute_enabled.get()}")
        print(f"  quick_reply_buttons存在: {bool(gui.quick_reply_buttons)}")
        
        # 测试第二次自动选择触发
        print("\n🎯 测试第二次自动选择触发:")
        
        # 记录原始的_perform_auto_select方法
        original_perform_method = gui._perform_auto_select
        perform_called = [False]  # 使用列表来在闭包中修改值
        
        def enhanced_perform_method():
            """增强的执行方法，添加诊断信息"""
            print("  [DEBUG] _perform_auto_select 被调用")
            perform_called[0] = True
            try:
                original_perform_method()
                print("  [DEBUG] _perform_auto_select 执行完成")
            except Exception as e:
                print(f"  [ERROR] _perform_auto_select 执行失败: {e}")
        
        # 替换方法
        gui._perform_auto_select = enhanced_perform_method
        
        # 调用触发检查
        gui._check_auto_select_trigger()
        print("  ✓ _check_auto_select_trigger 调用完成")
        
        # 等待一段时间看是否触发
        print("  等待自动选择触发...")
        time.sleep(4)  # 等待超过延迟时间
        
        # 检查是否被调用
        if perform_called[0]:
            print("  ✓ 第二次自动选择成功触发")
        else:
            print("  ✗ 第二次自动选择未触发 - 这是问题所在！")
        
        # 分析问题原因
        print("\n📊 问题分析:")
        
        # 检查_check_auto_select_trigger的逻辑
        print("  检查_check_auto_select_trigger逻辑:")
        condition1 = gui.auto_select_enabled.get()
        condition2 = not gui.auto_execute_enabled.get()
        condition3 = bool(gui.quick_reply_buttons)
        
        print(f"    条件1 - auto_select_enabled: {condition1}")
        print(f"    条件2 - not auto_execute_enabled: {condition2}")
        print(f"    条件3 - quick_reply_buttons存在: {condition3}")
        print(f"    总体条件满足: {condition1 and condition2 and condition3}")
        
        if condition1 and condition2 and condition3:
            print("  ✓ 触发条件满足，问题可能在定时器或其他地方")
        else:
            print("  ✗ 触发条件不满足，这是问题原因")
        
        # 检查是否有定时器问题
        print("\n  检查定时器状态:")
        print(f"    auto_execute_timer_id: {gui.auto_execute_timer_id}")
        
        # 清理
        gui.root.destroy()
        
        print("\n" + "=" * 70)
        print("🎯 诊断结果总结")
        print("=" * 70)
        
        if not perform_called[0]:
            print("❌ 问题确认：第二次自动选择未触发")
            print("\n可能的原因:")
            print("1. _check_auto_select_trigger 的触发条件过于严格")
            print("2. 快捷回复按钮创建后没有调用 _check_auto_select_trigger")
            print("3. 定时器设置有问题")
            print("4. 状态重置不正确")
        else:
            print("✅ 自动选择功能正常工作")
        
        return not perform_called[0]  # 返回True表示有问题
        
    except Exception as e:
        print(f"✗ 诊断过程出错: {e}")
        import traceback
        traceback.print_exc()
        return True

if __name__ == "__main__":
    has_issue = diagnose_auto_select_issue()
    if has_issue:
        print("\n🔧 需要修复自动选择功能")
        sys.exit(1)
    else:
        print("\n✅ 自动选择功能正常")
        sys.exit(0)
