#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
自动测试面板布局优化自动化测试脚本
"""

import sys
from pathlib import Path

# 添加项目根目录到Python路径
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

def test_layout_optimization():
    """自动化测试布局优化功能"""
    try:
        from deepseek_chat_client.gui import ChatGUI
        
        print("=" * 60)
        print("🧪 自动测试面板布局优化验证")
        print("=" * 60)
        
        # 创建GUI实例（不显示窗口）
        gui = ChatGUI()
        gui.root.withdraw()  # 隐藏窗口
        
        tests_passed = 0
        total_tests = 0
        
        # 测试1：检查新增的布局相关属性
        print("1. 检查布局相关属性...")
        total_tests += 1
        
        required_attrs = [
            'auto_test_layout_mode',
            'auto_test_is_collapsed',
            '_resize_timer_id',
            'auto_test_frame',
            'auto_test_content_frame',
            'auto_test_collapse_btn',
            'auto_test_quick_status'
        ]
        
        missing_attrs = []
        for attr in required_attrs:
            if not hasattr(gui, attr):
                missing_attrs.append(attr)
        
        if not missing_attrs:
            print("  ✓ 所有布局属性存在")
            tests_passed += 1
        else:
            print(f"  ✗ 缺少属性: {missing_attrs}")
        
        # 测试2：检查Grid布局控件
        print("2. 检查Grid布局控件...")
        total_tests += 1
        
        grid_widgets = [
            'auto_select_checkbox',
            'auto_execute_checkbox', 
            'delay_spinbox',
            'rounds_spinbox',
            'auto_test_control_btn',
            'auto_test_status_label'
        ]
        
        missing_widgets = []
        for widget in grid_widgets:
            if not hasattr(gui, widget):
                missing_widgets.append(widget)
        
        if not missing_widgets:
            print("  ✓ 所有Grid布局控件存在")
            tests_passed += 1
        else:
            print(f"  ✗ 缺少控件: {missing_widgets}")
        
        # 测试3：测试布局模式切换
        print("3. 测试布局模式切换...")
        total_tests += 1
        
        try:
            # 测试完整模式
            gui._apply_layout_mode("full")
            if gui.auto_test_layout_mode == "full":
                print("  ✓ 完整模式切换成功")
            else:
                print("  ✗ 完整模式切换失败")
                
            # 测试紧凑模式
            gui._apply_layout_mode("compact")
            if gui.auto_test_layout_mode == "compact":
                print("  ✓ 紧凑模式切换成功")
            else:
                print("  ✗ 紧凑模式切换失败")
                
            # 测试折叠模式
            gui._apply_layout_mode("collapsed")
            if gui.auto_test_layout_mode == "collapsed":
                print("  ✓ 折叠模式切换成功")
            else:
                print("  ✗ 折叠模式切换失败")
                
            tests_passed += 1
            
        except Exception as e:
            print(f"  ✗ 布局模式切换测试失败: {e}")
        
        # 测试4：测试面板折叠功能
        print("4. 测试面板折叠功能...")
        total_tests += 1
        
        try:
            # 测试折叠
            initial_state = gui.auto_test_is_collapsed.get()
            gui._toggle_auto_test_panel()
            new_state = gui.auto_test_is_collapsed.get()
            
            if new_state != initial_state:
                print("  ✓ 面板折叠切换成功")
                
                # 测试展开
                gui._toggle_auto_test_panel()
                final_state = gui.auto_test_is_collapsed.get()
                
                if final_state == initial_state:
                    print("  ✓ 面板展开切换成功")
                    tests_passed += 1
                else:
                    print("  ✗ 面板展开切换失败")
            else:
                print("  ✗ 面板折叠切换失败")
                
        except Exception as e:
            print(f"  ✗ 面板折叠功能测试失败: {e}")
        
        # 测试5：测试快速状态更新
        print("5. 测试快速状态更新...")
        total_tests += 1
        
        try:
            # 启用自动选择和自动执行
            gui.auto_select_enabled.set(True)
            gui.auto_execute_enabled.set(True)
            gui.current_auto_round = 5
            gui.auto_execute_rounds.set(10)
            
            # 更新快速状态
            gui._update_quick_status()
            
            # 检查快速状态文本
            quick_status_text = gui.auto_test_quick_status.cget('text')
            if "选择" in quick_status_text and "执行" in quick_status_text:
                print("  ✓ 快速状态更新成功")
                tests_passed += 1
            else:
                print(f"  ✗ 快速状态更新失败，当前文本: {quick_status_text}")
                
        except Exception as e:
            print(f"  ✗ 快速状态更新测试失败: {e}")
        
        # 测试6：测试布局模式逻辑
        print("6. 测试布局模式逻辑...")
        total_tests += 1

        try:
            # 直接测试不同模式的应用
            test_modes = ["full", "compact", "collapsed"]

            all_correct = True
            for mode in test_modes:
                # 直接应用布局模式
                gui._apply_layout_mode(mode)

                actual_mode = gui.auto_test_layout_mode
                if actual_mode == mode:
                    print(f"  ✓ {mode}模式应用成功")
                else:
                    print(f"  ✗ {mode}模式应用失败，实际: {actual_mode}")
                    all_correct = False

            # 测试布局切换逻辑
            print("  测试布局切换逻辑...")

            # 测试从full到compact
            gui._apply_layout_mode("full")
            gui._apply_layout_mode("compact")
            if gui.auto_test_layout_mode == "compact":
                print("  ✓ full -> compact 切换成功")
            else:
                print("  ✗ full -> compact 切换失败")
                all_correct = False

            # 测试从compact到collapsed
            gui._apply_layout_mode("collapsed")
            if gui.auto_test_layout_mode == "collapsed":
                print("  ✓ compact -> collapsed 切换成功")
            else:
                print("  ✗ compact -> collapsed 切换失败")
                all_correct = False

            if all_correct:
                tests_passed += 1

        except Exception as e:
            print(f"  ✗ 布局模式逻辑测试失败: {e}")
        
        # 输出测试结果
        print()
        print("=" * 60)
        print(f"测试结果: {tests_passed}/{total_tests} 通过")
        
        if tests_passed == total_tests:
            print("🎉 所有测试通过！布局优化实现成功！")
            print()
            print("优化功能总结：")
            print("✓ Grid布局管理器替代Pack布局")
            print("✓ 响应式设计支持不同窗口尺寸")
            print("✓ 面板折叠功能节省空间")
            print("✓ 自动布局模式切换")
            print("✓ 快速状态显示")
            print("✓ 窗口大小变化监听")
            return True
        else:
            print("❌ 部分测试失败，需要修复问题")
            return False
        
    except Exception as e:
        print(f"✗ 测试过程中发生错误: {e}")
        import traceback
        traceback.print_exc()
        return False

def main():
    """主函数"""
    print("开始自动测试面板布局优化验证...")
    print()
    
    success = test_layout_optimization()
    
    if success:
        print()
        print("📋 使用建议：")
        print("1. 在不同分辨率的显示器上测试")
        print("2. 拖拽窗口边缘测试自适应效果")
        print("3. 使用折叠功能在小屏幕上节省空间")
        print("4. 观察紧凑模式下的控件调整")
    
    return success

if __name__ == "__main__":
    try:
        success = main()
        sys.exit(0 if success else 1)
    except KeyboardInterrupt:
        print("\n用户中断测试")
        sys.exit(1)
    except Exception as e:
        print(f"测试失败: {e}")
        sys.exit(1)
