#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试实际应用中的自动选择问题
"""

import sys
import os
from pathlib import Path
import time
import threading

# 添加项目根目录到Python路径
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

def test_real_auto_select_issue():
    """测试实际的自动选择问题"""
    try:
        print("=" * 70)
        print("🔍 测试实际应用中的自动选择问题")
        print("=" * 70)
        
        from deepseek_chat_client.gui import ChatGUI
        import tkinter as tk
        import tkinter.ttk as ttk
        
        # 创建GUI实例
        gui = ChatGUI()
        print("✓ GUI实例创建成功")
        
        # 启用自动选择功能
        gui.auto_select_enabled.set(True)
        gui.auto_execute_enabled.set(False)
        print("✓ 启用自动选择功能")
        
        # 跟踪自动选择调用
        auto_select_calls = []
        original_check_trigger = gui._check_auto_select_trigger
        
        def tracked_check_trigger():
            """跟踪_check_auto_select_trigger调用"""
            call_time = time.time()
            auto_select_calls.append(call_time)
            print(f"  [TRIGGER] _check_auto_select_trigger 被调用 (第{len(auto_select_calls)}次)")
            try:
                original_check_trigger()
            except Exception as e:
                print(f"  [ERROR] _check_auto_select_trigger 执行出错: {e}")
        
        # 替换方法
        gui._check_auto_select_trigger = tracked_check_trigger
        
        # 跟踪_perform_auto_select调用
        perform_calls = []
        original_perform = gui._perform_auto_select
        
        def tracked_perform():
            """跟踪_perform_auto_select调用"""
            call_time = time.time()
            perform_calls.append(call_time)
            print(f"  [PERFORM] _perform_auto_select 被调用 (第{len(perform_calls)}次)")
            try:
                original_perform()
            except Exception as e:
                print(f"  [ERROR] _perform_auto_select 执行出错: {e}")
        
        # 替换方法
        gui._perform_auto_select = tracked_perform
        
        # 模拟第一次快捷回复显示（通过_parse_and_show_quick_replies）
        print("\n🎯 第一次快捷回复显示测试:")
        
        # 模拟AI响应内容
        test_content = '''这是一个测试响应。

```json
{
    "quick_replies": ["选项1", "选项2", "选项3"]
}
```

请选择一个选项。'''
        
        print("  调用 _parse_and_show_quick_replies...")
        gui._parse_and_show_quick_replies(test_content)
        
        print(f"  _check_auto_select_trigger 调用次数: {len(auto_select_calls)}")
        
        # 运行事件循环等待自动选择
        print("  运行事件循环等待自动选择...")
        start_time = time.time()
        while time.time() - start_time < 4:
            gui.root.update()
            time.sleep(0.1)
        
        print(f"  _perform_auto_select 调用次数: {len(perform_calls)}")
        
        # 模拟用户点击快捷回复按钮
        print("\n🖱️ 模拟用户点击快捷回复按钮:")
        
        # 找到第一个按钮并点击
        clicked_button = None
        for widget in gui.quick_reply_buttons:
            if hasattr(widget, 'invoke') and hasattr(widget, 'cget'):
                try:
                    button_text = widget.cget('text')
                    if button_text and button_text != "快捷回复:":
                        print(f"  点击按钮: {button_text}")
                        clicked_button = widget
                        break
                except:
                    continue
        
        if clicked_button:
            # 模拟点击
            clicked_button.invoke()
            print("  ✓ 按钮点击完成")
            
            # 等待发送完成
            print("  等待消息发送完成...")
            time.sleep(1)
            
            # 模拟API响应完成，显示新的快捷回复
            print("\n🎯 第二次快捷回复显示测试:")
            
            test_content2 = '''这是第二个测试响应。

```json
{
    "quick_replies": ["选项A", "选项B", "选项C"]
}
```

请选择下一个选项。'''
            
            print("  调用 _parse_and_show_quick_replies...")
            gui._parse_and_show_quick_replies(test_content2)
            
            print(f"  _check_auto_select_trigger 调用次数: {len(auto_select_calls)}")
            
            # 运行事件循环等待第二次自动选择
            print("  运行事件循环等待第二次自动选择...")
            start_time = time.time()
            while time.time() - start_time < 4:
                gui.root.update()
                time.sleep(0.1)
            
            print(f"  _perform_auto_select 调用次数: {len(perform_calls)}")
            
        else:
            print("  ✗ 未找到可点击的按钮")
        
        # 分析结果
        print("\n📊 测试结果分析:")
        print(f"  总_check_auto_select_trigger调用次数: {len(auto_select_calls)}")
        print(f"  总_perform_auto_select调用次数: {len(perform_calls)}")
        
        if len(auto_select_calls) >= 2:
            print("  ✓ 触发检查正常")
        else:
            print("  ✗ 触发检查异常")
        
        if len(perform_calls) >= 2:
            print("  ✓ 自动选择执行正常")
            result = False  # 没有问题
        else:
            print("  ✗ 自动选择执行异常 - 这是问题所在！")
            result = True  # 有问题
        
        # 详细时间分析
        print("\n⏰ 时间分析:")
        for i, call_time in enumerate(auto_select_calls):
            print(f"  触发检查{i+1}: {call_time}")
        
        for i, call_time in enumerate(perform_calls):
            print(f"  自动选择{i+1}: {call_time}")
        
        # 清理
        gui.root.destroy()
        
        return result
        
    except Exception as e:
        print(f"✗ 测试过程出错: {e}")
        import traceback
        traceback.print_exc()
        return True

if __name__ == "__main__":
    has_issue = test_real_auto_select_issue()
    if has_issue:
        print("\n🔧 确认存在自动选择问题")
        sys.exit(1)
    else:
        print("\n✅ 自动选择功能正常")
        sys.exit(0)
